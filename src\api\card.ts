import axios from 'axios';
import { tokenManager } from './auth';
import { HttpResponse } from './interceptor';

// 请求参数接口
export interface CardInfoDTO {
  wechatOfficialAccountCode: string; // 微信公众号编码: funLoopLand - 环游， esports - 华立电竞， card - 卡片嘉年华
  offset?: number; // 偏移量，默认从 0 开始
  count?: number; // 获取数量，默认 10 条数据
}

// 卡券项接口
export interface CardItem {
  card_id: string; // 卡券唯一标识
  title: string; // 卡券标题
  description: string; // 卡券描述
}

// 响应数据接口
export interface CardInfoData {
  total_count: number; // 总条数
  item_count: number; // 当前页数据条数
  item: CardItem[];
}

export interface CardInfoResponse {
  msg: string;
  code: number;
  data: CardInfoData;
}

// 获取卡券信息
export async function getCardInfo(params: CardInfoDTO) {
  try {
    const token = await tokenManager.getValidToken();

    const response = await axios.post('/api/wechat/official/accounts/getCardInfo', params, {
      headers: {
        'token': token,
        'Content-Type': 'application/json',
      },
    });

    // 检查响应是否存在
    if (!response) {
      throw new Error('获取卡券信息失败: 服务器响应格式错误');
    }

    return response;
  } catch (error: any) {
    console.error('获取卡券信息失败:', error);

    // 如果是token相关错误，清除token缓存
    if (error?.message && error.message.includes('token')) {
      tokenManager.clearToken();
    }

    // 如果是axios错误，提供更详细的错误信息
    if (error?.response) {
      console.error('服务器响应错误:', error.response.status, error.response.data);
      throw new Error(`获取卡券信息失败: 服务器错误 ${error.response.status}`);
    } else if (error?.request) {
      console.error('网络请求失败:', error.request);
      throw new Error('获取卡券信息失败: 网络连接错误');
    } else {
      throw error;
    }
  }
}