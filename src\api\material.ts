import axios from 'axios';
import type { HttpResponse } from './interceptor';
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export interface MaterialItem {
  id: number;
  sequence: number;
  name: string;
  fileType: string;
  size: string;
  createTime: string;
  url?: string;
  thumbnail?: string;
  videoTitle?: string;
  videoDescription?: string;
  updateTime?: string;
}

// 新增接口数据结构
export interface MaterialRecord {
  id?: string;
  materialName: string;
  filePath: string;
  fileName: string;
  fileType: string;
  status: string;
  createTime: string;
  updateTime: string;
  title?: string; // 视频标题
  description?: string; // 视频描述
}

export interface MaterialListParams {
  page?: number;
  size?: number;
  fileType: string;
  name?: string;
  expression?: string;
}

// 修改列表响应结构以匹配实际接口返回格式
export interface MaterialListResponse {
  content: MaterialRecord[];
  size: number;
  number: number;
  totalPages: number;
  totalElements: number;
}

// 获取素材列表
export function getMaterialList(params: MaterialListParams) {
  // 手动构建查询字符串以避免自动编码
  const baseUrl = `/api/ma-manage/${tenantId}/${buCode}/material_record`;
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (key === 'expression') {
        // 对于expression参数，使用自定义方式添加以保持原始格式
        searchParams.append(key, value);
      } else {
        searchParams.append(key, String(value));
      }
    }
  });
  
  // 使用自定义编码方式处理查询参数
  let queryString = '';
  for (const [key, value] of searchParams.entries()) {
    if (queryString) queryString += '&';
    // 对于expression参数，保持原始空格格式
    if (key === 'expression') {
      queryString += `${key}=${encodeURIComponent(value).replace(/%20/g, ' ')}`;
    } else {
      queryString += `${key}=${encodeURIComponent(value)}`;
    }
  }
  
  return axios.get<HttpResponse<MaterialListResponse>>(`${baseUrl}?${queryString}`);
}

// 新增无分页的素材列表接口
export function getMaterialListNoPage(params: Omit<MaterialListParams, 'page' | 'size'>) {
  // 手动构建查询字符串以避免自动编码
  const baseUrl = `/api/ma-manage/${tenantId}/${buCode}/material_record/list`;
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (key === 'expression') {
        // 对于expression参数，使用自定义方式添加以保持原始格式
        searchParams.append(key, value);
      } else {
        searchParams.append(key, String(value));
      }
    }
  });
  
  // 使用自定义编码方式处理查询参数
  let queryString = '';
  for (const [key, value] of searchParams.entries()) {
    if (queryString) queryString += '&';
    // 对于expression参数，保持原始空格格式
    if (key === 'expression') {
      queryString += `${key}=${encodeURIComponent(value).replace(/%20/g, ' ')}`;
    } else {
      queryString += `${key}=${encodeURIComponent(value)}`;
    }
  }
  
  return axios.get<HttpResponse<MaterialRecord[]>>(`${baseUrl}?${queryString}`);
}

// 删除单个素材
export function deleteMaterial(id: number) {
  return axios.delete<HttpResponse<null>>(`/api/ma-manage/${tenantId}/${buCode}/material_record/${id}`);
}

// 批量删除素材
export function batchDeleteMaterial(ids: number[]) {
  return axios.delete<HttpResponse<null>>(`/api/ma-manage/${tenantId}/${buCode}/material_record/batch`, {
    data: { ids },
  });
}

// 上传素材
export function uploadMaterial(data: MaterialRecord) {
  return axios.post<HttpResponse<MaterialRecord>>(`/api/ma-manage/${tenantId}/${buCode}/material_record`, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 获取单个素材详情
export function getMaterial(id: string) {
  return axios.get<HttpResponse<MaterialRecord>>(`/api/ma-manage/${tenantId}/${buCode}/material_record/${id}`);
}

// 更新素材信息
export function updateMaterial(data: Partial<MaterialRecord>) {
  return axios.put<HttpResponse<MaterialRecord>>(`/api/ma-manage/${tenantId}/${buCode}/material_record`, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 新增素材
export function addMaterial(data: MaterialRecord) {
  return axios.post<HttpResponse<MaterialRecord>>(`/api/ma-manage/${tenantId}/${buCode}/material_record/`, data);
}