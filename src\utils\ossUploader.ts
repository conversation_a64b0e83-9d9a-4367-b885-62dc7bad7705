import axios from 'axios';
import { useBussinessUnitStore } from "@/store";

const userBussinessUnitStore = useBussinessUnitStore();
const buCode = userBussinessUnitStore.currentBussinessUnit?.code;
const tenantId = userBussinessUnitStore.currentBussinessUnit?.tenantId;

export interface UploadToken {
  id: string;
  secret: string;
  securityToken: string;
  bucket: string;
}

export interface FileUploadResult {
  url: string;
  name: string;
}

// 获取上传凭证的参数接口
interface UploadTokenParams {
  // 根据实际API需求定义参数
}

// 获取上传凭证的响应接口
interface UploadTokenResponse {

    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
    bucketName: string;

}

// 获取上传凭证
export function getUploadTokenAPI(params?: UploadTokenParams): Promise<UploadTokenResponse> {
  return axios.post(`/api/ma-manage/${tenantId}/${buCode}/oss/getToken`, {
    params
  });
}

/**
 * 获取上传凭证
 * @returns Promise<UploadToken>
 */
export const getUploadToken = async (): Promise<UploadToken> => {
  // 调用后端API获取上传凭证
  const response = await getUploadTokenAPI();
  const { accessKeyId, accessKeySecret, securityToken ,bucketName } = response;

  return {
    id: accessKeyId,
    secret: accessKeySecret,
    securityToken: securityToken,
    bucket: bucketName || 'test-wahlap-cdp'
  };
};

/**
 * 初始化OSS客户端
 * @param token 上传凭证
 * @returns OSS客户端实例
 */
export const initOSSClient = (token: UploadToken): any => {
  // 使用全局引入的OSS对象
  return new (window as any).OSS({
    accessKeyId: token.id,
    accessKeySecret: token.secret,
    stsToken: token.securityToken,
    bucket: token.bucket,
    region: "oss-cn-shenzhen" 
  });
};

/**
 * 生成文件名
 * @param file 文件对象
 * @param type 素材类型
 * @returns 生成的文件名
 */
export const generateFileName = (file: File, type: string): string => {
  const name = new Date().getTime();
  const S4 = () => {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  };

  const getUUID = () => {
    return S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4();
  };

  const getFileExtension = (filename: string): string => {
    return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
  };

  const fileName = "/sts/" + getUUID() + name + "." + getFileExtension(file.name);
  return fileName;
};

/**
 * 上传文件到OSS
 * @param file 要上传的文件
 * @param type 素材类型
 * @returns Promise<FileUploadResult>
 */
export const uploadToOSS = async (file: File, type: string): Promise<FileUploadResult> => {
  try {
    // 获取上传凭证
    const token = await getUploadToken();

    // 初始化OSS客户端
    const ossClient = initOSSClient(token);

    // 生成文件名
    const fileName = generateFileName(file, type);

    // 上传到OSS - 使用multipartUpload方法避免CORS问题
    const result = await ossClient.put(fileName, file, {
      headers: {
        "x-oss-forbid-overwrite": "true"
      }
    });
    // console.log('result', result);
    return {
      url: result.res.requestUrls[0],
      name: result.name
    };
  } catch (error) {
    console.error('OSS上传错误详情:', error);
    throw new Error('上传失败: ' + (error as Error).message);
  }
};