<template>
  <div>
    <module main>
      <template #filter></template>
      <template #search></template>
      <template #action>
        <!-- 标签页 -->
        <a-tabs v-model:active-key="activeTab" @change="onTabChange">
          <a-tab-pane key="all" :title="t('material.tabs.all')" />
          <a-tab-pane key="image" :title="t('material.tabs.image')" />
          <a-tab-pane key="audio" :title="t('material.tabs.audio')" />
          <a-tab-pane key="video" :title="t('material.tabs.video')" />
          <a-tab-pane key="file" :title="t('material.tabs.file')" />
        </a-tabs>
      </template>
      <template #context>
        <a-button type="primary" @click="uploadMaterial()">
          <template #icon>
            <icon-plus />
          </template>
          {{t('material.button.upload')}}
        </a-button>
        <a-button @click="selectAll()">
          全选
        </a-button>
        <a-button 
          :disabled="selectedRowKeys.length === 0" 
          @click="batchDelete()"
        >
          {{t('material.button.delete')}}
        </a-button>
      </template>
      <template #main>
        <!-- 表格 -->
        <a-table 
          ref="table" 
          :bordered="false" 
          :data="filteredDataSource" 
          :pagination="false"
          v-model:selectedKeys="selectedRowKeys"
          :row-selection="{
            type: 'checkbox',
            showCheckedAll: true,
            width: 60
          }"
          :loading="loading"
          row-key="id"
        >
          <template #columns>
            <a-table-column :title="t('material.column.sequence')" :width="80">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column :title="t('material.column.name')" data-index="name" :ellipsis="true" :tooltip="true" />
            <!-- <a-table-column :title="t('material.column.type')" data-index="type" :width="180">
              <template #cell="{ record }">
                <a-tag :color="getTypeColor(record.fileType)">{{ record.fileType }}</a-tag>
              </template>
            </a-table-column> -->
            <a-table-column :title="t('material.column.type')" data-index="type" :width="180">
              <template #cell="{ record }">
                <a-tag :color="getTypeColor(record.fileType)">{{ getTypeText(record.fileType) }}</a-tag>
              </template>
            </a-table-column>

            <a-table-column 
              :title="t('material.column.createTime')" 
              data-index="createTime" 
              :width="180"
              :sortable="{ sortDirections: ['ascend', 'descend'] }"
              @sorter-change="handleSortChange"
            />
            <a-table-column :title="t('material.column.action')" align="center" :width="300">
              <template #cell="{ record }">
                <a-space :size="2">
                  <a-button type="text" size="small" class="action-btn" @click="preview(record)">
                    {{t('material.operation.preview')}}
                  </a-button>
                  <a-button type="text" size="small" class="action-btn" @click="edit(record)">
                    {{t('material.operation.edit')}}
                  </a-button>
                  <a-button type="text" size="small" class="action-btn delete-btn" @click="deleteData(record.id)">
                    {{t('material.operation.delete')}}
                  </a-button>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </template>
    </module>
  </div>
</template>

<script>
import { provide, ref, getCurrentInstance, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { Message, Modal } from "@arco-design/web-vue";
import { getMaterialList, deleteMaterial, batchDeleteMaterial, getMaterial, updateMaterial } from "@/api/material";
import { shortDatetime } from "@/utils/date";

export default {
  setup() {
    const {
      proxy: { t }
    } = getCurrentInstance();
    const router = useRouter();
    
    const module = ref({
      entityIdField: "id",
      viewPath: "/material/material",
      showCard: true,
      showBtn: true,
      breadcrumb: [
        {
          name: t('material.title'),
          path: "/material/material"
        }
      ]
    });
    
    const filter = ref([
      {
        field: "name",
        label: t('material.name'),
        component: "a-input",
        operate: "like",
        placeholder: t('material.reminder.input_activity_name'),
        comment: true,
        value: ""
      }
    ]);
    
    // 状态管理
    const loading = ref(false);
    const activeTab = ref("all");
    const selectedRowKeys = ref([]);
    
    // 数据源
    const dataSource = ref([]);
    
    // 分页设置
    const pagination = ref({
      page: 1,
      size: 20,
      total: 0,
      showPageSize: true
    });
    
    // 过滤后的数据源（现在通过 API 参数过滤，这里直接返回）
    const filteredDataSource = computed(() => {
      return dataSource.value;
    });
    
    // 表格行选择配置
    const tableRowSelection = computed(() => ({
      type: "checkbox",
      showCheckedAll: true,
      selectedRowKeys: selectedRowKeys.value,
      onChange: (rowKeys) => {
        selectedRowKeys.value = rowKeys;
      },
      onSelect: (record, selected, selectedRows, nativeEvent) => {
        // 这个回调会在 onChange 之前触发，所以我们主要依赖 onChange
      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        // 这个也会触发 onChange，所以主要逻辑在 onChange 中处理
      }
    }));
    
    const formRef = ref({});

    // 获取类型颜色
    const getTypeColor = (type) => {
      const colorMap = {
        "image": "blue",
        "video": "green",
        "audio": "orange",
        "file": "gray"
      };
      return colorMap[type] || "gray";
    };

    // 获取类型文本
    const getTypeText = (type) => {
      const typeMap = {
        "image": t('material.tabs.image'),
        "video": t('material.tabs.video'),
        "audio": t('material.tabs.audio'),
        "file": t('material.tabs.file')
      };
      return typeMap[type] || type;
    };

    // 根据文件名确定素材类型
    // const determineTypeByName = (fileName) => {
    //   if (!fileName) return "file";

    //   const extension = fileName.toLowerCase().split('.').pop();
    //   const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    //   const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'];
    //   const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'];

    //   if (imageExtensions.includes(extension)) {
    //     return "image";
    //   } else if (videoExtensions.includes(extension)) {
    //     return "video";
    //   } else if (audioExtensions.includes(extension)) {
    //     return "audio";
    //   } else {
    //     return "file";
    //   }
    // };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      // 如果已经是格式化的字符串，直接返回
      if (typeof dateString === 'string' && dateString.includes('-')) {
        return shortDatetime(dateString);
      }
      // 如果是时间戳，转换为日期
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '-');
    };

    // 静态数据
    // const staticMaterialData = [
    //   {
    //     id: 1,
    //     name: "2025年三期NDD（主营销）物料清单.xlsx",
    //     type: "文件",
    //     size: "2.5MB",
    //     createTime: "2025-07-29 09:31"
    //   },
    //   {
    //     id: 2,
    //     name: "营销花卉75ml.png",
    //     type: "图片",
    //     size: "856KB",
    //     createTime: "2025-07-08 15:53"
    //   },
    //   {
    //     id: 3,
    //     name: "1456_1745477693.mp4",
    //     type: "视频",
    //     size: "12.3MB",
    //     createTime: "2025-07-07 18:01"
    //   },
    //   {
    //     id: 4,
    //     name: "微信图片_20220314100523.jpg",
    //     type: "图片",
    //     size: "2.1MB",
    //     createTime: "2025-07-04 15:17"
    //   },
    //   {
    //     id: 5,
    //     name: "234.txt",
    //     type: "文件",
    //     size: "1KB",
    //     createTime: "2025-07-04 15:17"
    //   }
    // ];

    // 获取素材列表 - 修复参数传递和数据处理逻辑
    const bindData = async (expression) => {
      loading.value = true;
      try {
        // 解析表达式获取搜索条件
        let searchName = '';
        if (expression && expression.includes('name')) {
          const nameMatch = expression.match(/name\s+like\s+'([^']+)'/);
          if (nameMatch) {
            searchName = nameMatch[1];
          }
        }
        
        // 构建expression查询条件
        let expressionQuery = 'status eq 0';
        if (activeTab.value !== 'all') {
          expressionQuery += ` AND fileType eq ${activeTab.value}`;
        }
        // 添加名称模糊搜索条件
        if (searchName) {
          expressionQuery += ` AND materialName like ${searchName}`;
        }
        
        const params = {
          expression: expressionQuery,
          page: pagination.value.page - 1, // 后端分页从0开始
          size: pagination.value.size,
          sort: 'createTime,desc' // 添加默认按创建时间倒序排序
        };
        
        const response = await getMaterialList(params);

        // console.log('API响应数据:', response);

        // 根据您提供的错误信息，API返回的数据结构是Spring Boot分页格式
        // axios拦截器已经返回了response.data，所以response就是实际的数据
        if (response && response.content && Array.isArray(response.content)) {
          // 直接处理Spring Boot分页格式的响应
          dataSource.value = response.content.map(item => ({
            id: item.id,
            name: item.materialName || item.name,
            fileType: item.fileType || determineTypeByName(item.fileName),
            size: item.size || '未知',
            createTime: formatDate(item.createTime),
            updateTime: formatDate(item.updateTime),
            url: item.filePath
          }));
          pagination.value.total = response.totalElements || 0;
          // console.log('处理后的数据源:', dataSource.value);
        } else if (response && response.data && response.data.content && Array.isArray(response.data.content)) {
          // 如果数据在data字段中
          const responseData = response.data;
          dataSource.value = responseData.content.map(item => ({
            id: item.id,
            name: item.materialName || item.name,
            fileType: item.fileType || determineTypeByName(item.fileName),
            size: item.size || '未知',
            createTime: formatDate(item.createTime),
            updateTime: formatDate(item.updateTime),
            url: item.filePath
          }));
          pagination.value.total = responseData.totalElements || 0;
          // console.log('处理后的数据源:', dataSource.value);
        } else if (response && (response.code === 20000 || response.code === '20000')) {
          // 处理标准响应格式
          const responseData = response.data || response;
          if (responseData.list) {
            dataSource.value = responseData.list.map(item => ({
              ...item,
              fileType: item.fileType || determineTypeByName(item.fileName)
            })) || [];
            pagination.value.total = responseData.total || 0;
          } else {
            dataSource.value = [];
            pagination.value.total = 0;
          }
        } else {
          console.error('获取素材列表失败，响应数据:', response);
          Message.error(response?.msg || '获取素材列表失败');
          // 如果 API 失败，使用静态数据作为后备，并根据搜索名称过滤
          let filteredData = staticMaterialData;
          if (searchName) {
            filteredData = staticMaterialData.filter(item =>
              item.name.toLowerCase().includes(searchName.toLowerCase())
            );
          }
          dataSource.value = filteredData.map(item => ({
            ...item,
            fileType: item.type === "图片" ? "image" : 
                     item.type === "视频" ? "video" : 
                     item.type === "音频" ? "audio" : "file"
          }));
          pagination.value.total = filteredData.length;
        }
      } catch (error) {
        console.error('获取素材列表时发生错误:', error);
        Message.error('获取素材列表失败，使用本地数据');
        // 如果 API 调用失败，使用静态数据作为后备，并根据搜索名称过滤
        let searchName = '';
        if (expression && expression.includes('name')) {
          const nameMatch = expression.match(/name\s+like\s+'([^']+)'/);
          if (nameMatch) {
            searchName = nameMatch[1];
          }
        }
        let filteredData = staticMaterialData;
        if (searchName) {
          filteredData = staticMaterialData.filter(item => 
            item.name.toLowerCase().includes(searchName.toLowerCase())
          );
        }
        dataSource.value = filteredData.map(item => ({
          ...item,
          fileType: item.type === "图片" ? "image" : 
                   item.type === "视频" ? "video" : 
                   item.type === "音频" ? "audio" : "file"
        }));
        pagination.value.total = filteredData.length;
      } finally {
        loading.value = false;
      }
    };

    // 标签页切换
    const onTabChange = (key) => {
      activeTab.value = key;
      selectedRowKeys.value = [];
      pagination.value.page = 1;
      bindData();
    };

    // 分页变化
    const onPageChange = (page) => {
      pagination.value.page = page;
      bindData();
    };

    const onPageSizeChange = (pageSize) => {
      pagination.value.size = pageSize;
      pagination.value.page = 1;
      bindData();
    };

    // 排序变化
    const handleSortChange = (dataIndex, direction) => {
      console.log('排序变化:', dataIndex, direction);
      // 这里可以添加排序逻辑，暂时不实现
    };

    // 全选功能
    const selectAll = () => {
      if (selectedRowKeys.value.length === filteredDataSource.value.length) {
        // 如果已经全选，则取消全选
        selectedRowKeys.value = [];
      } else {
        // 否则全选当前页面的所有数据
        selectedRowKeys.value = filteredDataSource.value.map(item => item.id);
      }
    };

    // 上传素材
    const uploadMaterial = () => {
      router.push("/material/upload");
    };

    // 批量删除
    const batchDelete = () => {
      if (selectedRowKeys.value.length === 0) {
        Message.warning(t('material.reminder.select_items_to_delete'));
        return;
      }
      
      Modal.confirm({
        title: t('material.reminder.delete_material'),
        content: t('material.reminder.batch_delete_confirm', { count: selectedRowKeys.value.length }),
        onOk: async () => {
          try {
            // 批量逻辑删除，将status从0改为1
            const updatePromises = selectedRowKeys.value.map(async (id) => {
              // 首先获取素材详情
              const detailResponse = await getMaterial(id);
              const materialData = detailResponse.data || detailResponse;
              
              // 更新status为1实现逻辑删除
              const updateData = {
                ...materialData,
                status: '1'
              };
              
              return updateMaterial(updateData);
            });
            
            // 等待所有更新操作完成
            await Promise.all(updatePromises);
            
            Message.success(`已删除 ${selectedRowKeys.value.length} 个素材`);
            selectedRowKeys.value = [];
            bindData();
          } catch (error) {
            console.error('批量删除过程中发生错误:', error);
            Message.error(error.message || '删除失败');
          }
        }
      });
    };

    // 导出数据
    const exportData = () => {
      Message.info("导出功能待开发");
    };

    // 预览素材
    const preview = (item) => {
      // 将OSS URL转换为CDN URL
      let previewUrl = item.url;
      if (previewUrl && previewUrl.includes('test-wahlap-cdp.oss-cn-shenzhen.aliyuncs.com')) {
        previewUrl = previewUrl.replace(
          'https://test-wahlap-cdp.oss-cn-shenzhen.aliyuncs.com',
          'https://test-cdp-cdn.wahlap.net'
        );
      }
      window.open(previewUrl, '_blank', 'noopener,noreferrer');
    };

    // 编辑数据
    const edit = (item) => {
      router.push({
        path: `/material/edit/${item.id}`
      });
    };

    // 删除单个素材
    const deleteData = async (id) => {
      Modal.confirm({
        title: t('material.reminder.delete_material'),
        content: t('material.reminder.data_irretrievable_warning'),
        onOk: async () => {
          try {
            // 首先获取素材详情
            const detailResponse = await getMaterial(id);
            
            // 逻辑删除，将status从0改为1
            const materialData = detailResponse.data || detailResponse;
            const updateData = {
              ...materialData,
              status: '1'
            };
            
            await updateMaterial(updateData);
            Message.success("删除成功");
            bindData();
          } catch (error) {
            console.error('删除过程中发生错误:', error);
            Message.error(error.message || '删除失败');
          }
        }
      });
    };

    // 组件挂载时获取数据
    onMounted(() => {
      bindData();
    });

    const setup = {
      t,
      module,
      filter,
      formRef,
      dataSource,
      filteredDataSource,
      pagination,
      loading,
      activeTab,
      selectedRowKeys,
      tableRowSelection,
      getTypeColor,
      getTypeText,
      onTabChange,
      onPageChange,
      onPageSizeChange,
      handleSortChange,
      selectAll,
      uploadMaterial,
      batchDelete,
      exportData,
      preview,
      edit,
      deleteData,
      bindData
    };
    
    provide("main", setup);
    return setup;
  }
};
</script>

<style lang="less" scoped>
:deep(.arco-table-th) {
  background-color: #fafafa;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.arco-checkbox) {
  pointer-events: auto !important;
}

:deep(.arco-table-selection-col) {
  width: 60px !important;
  min-width: 60px !important;
}

:deep(.arco-table-selection-col .arco-checkbox) {
  width: 16px !important;
  height: 16px !important;
}

:deep(.arco-table-selection-col .arco-checkbox-icon) {
  width: 16px !important;
  height: 16px !important;
}

:deep(.arco-table) {
  min-width: 100% !important;
}

:deep(.arco-table-container) {
  overflow-x: auto !important;
}



</style>
